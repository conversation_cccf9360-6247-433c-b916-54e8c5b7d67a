# 普法工作管理系统统计接口
# 用于统计平台收集官方信息总数和今日增加数

from datetime import datetime, date
from flask import jsonify
from flask_login import login_required
from sqlalchemy import func, and_
from web.routes import main_routes
from web.models.faxuan.FXArticleRecords import FxArticleRecords
from web.models.faxuan.FXAnalysusSummary import AnalysisSummary
from web import db


@main_routes.route('/api/faxuan/statistics/info_summary', methods=['GET'])
# @login_required
def get_info_summary():
    """
    获取平台收集官方信息总数和今日增加数统计
    ---
    tags:
      - 普法统计
    description:
        获取平台收集官方信息总数和今日增加数的统计接口
    responses:
      200:
          description: 统计信息
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 响应状态码
              msg:
                type: string
                description: 响应消息
              data:
                type: object
                properties:
                  total_count:
                    type: integer
                    description: 平台收集官方信息总数
                  today_count:
                    type: integer
                    description: 今日新增数量
                  today_date:
                    type: string
                    description: 统计日期
      500:
          description: 服务器错误
    """
    try:
        # 获取今天的日期
        today = date.today()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())
        
        # 查询平台收集官方信息总数
        total_count = db.session.query(func.count(FxArticleRecords.id)).scalar() or 0
        
        # 查询今日新增数量（基于create_time字段）
        today_count = db.session.query(func.count(FxArticleRecords.id)).filter(
            and_(
                FxArticleRecords.create_time >= today_start,
                FxArticleRecords.create_time <= today_end
            )
        ).scalar() or 0
        
        # 构建响应数据
        result = {
            'total_count': total_count,
            'today_count': today_count,
            'today_date': today.strftime('%Y-%m-%d')
        }
        
        return jsonify({
            'code': 200,
            'msg': '操作成功',
            'data': result
        })
        
    except Exception as e:
        return jsonify({
            'code': 500,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500





@main_routes.route('/api/faxuan/statistics/unit_type_total', methods=['GET'])
# @login_required
def get_unit_type_total_stats():
    """
    获取各类型行业系统发布总数统计
    ---
    tags:
      - 普法统计
    description:
        获取各类型行业系统发布总数统计，基于fx_analysis_summary表，用于前端图表展示
    responses:
      200:
          description: 各类型行业系统发布总数统计
      500:
          description: 服务器错误
    """
    try:
         # 查询分析汇总表所有数据
        summary_records = db.session.query(AnalysisSummary).all()

        if not summary_records:
            return jsonify({
                'code': 200,
                'msg': '操作成功',
                'data': {
                    'chart_data': [],
                    'x_axis_data': [],
                    'y_axis_data': [],
                    'summary_data': [],
                    'total_articles': 0
                }
            })

        # 按单位属性汇总数据
        unit_property_stats = {}
        summary_data = []

        for record in summary_records:
            unit_property = record.unit_property

            # 汇总单位属性数据
            if unit_property not in unit_property_stats:
                unit_property_stats[unit_property] = 0
            unit_property_stats[unit_property] += record.total_articles

            # 添加详细数据
            summary_data.append(record.to_json())

        # 定义标准的单位属性顺序（与前端保持一致）
        standard_properties = ['市级单位', '区级单位', '国企', '高校']

        # 构建图表数据，确保包含所有标准类型
        chart_data = []
        x_axis_data = []
        y_axis_data = []

        for unit_property in standard_properties:
            count = unit_property_stats.get(unit_property, 0)
            chart_data.append({
                'unit_property': unit_property,
                'total_articles': count
            })
            x_axis_data.append(unit_property)
            y_axis_data.append(count)

        # 按发布数量排序汇总数据
        summary_data.sort(key=lambda x: (x['unit_property'], -x['total_articles']))

        # 构建响应数据
        result = {
            'chart_data': chart_data,
            'x_axis_data': x_axis_data,  # 前端ECharts需要的x轴数据
            'y_axis_data': y_axis_data,  # 前端ECharts需要的y轴数据
            'summary_data': summary_data,  # 详细汇总数据
            'total_articles': sum(y_axis_data)  # 总发布数
        }

        return jsonify({
            'code': 200,
            'msg': '操作成功',
            'data': result
        })

    except Exception as e:
        return jsonify({
            'code': 500,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500
