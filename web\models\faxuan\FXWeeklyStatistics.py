from datetime import datetime
from web import db

class FxWeeklyStatistics(db.Model):
    """
    普法工作管理系统本周统计数据模型
    用于存储本周各单位发布数统计的缓存数据
    """
    __tablename__ = 'fx_weekly_statistics'

    id = db.Column(db.<PERSON>Integer, primary_key=True, autoincrement=True, comment='自增主键')
    week_start = db.Column(db.Date, nullable=False, comment='统计周开始日期')
    week_end = db.Column(db.Date, nullable=False, comment='统计周结束日期')
    unit_name = db.Column(db.String(100), nullable=False, comment='单位名称')
    article_count = db.Column(db.Integer, nullable=False, default=0, comment='本周发布文章数量')
    rank_position = db.Column(db.Integer, nullable=True, comment='排名位置')
    create_time = db.Column(db.DateTime, nullable=False, default=datetime.now, comment='记录创建时间')
    update_time = db.Column(db.DateTime, onupdate=datetime.now, comment='记录更新时间')

    # 创建复合索引，提高查询效率
    __table_args__ = (
        db.Index('idx_week_unit', 'week_start', 'week_end', 'unit_name'),
        db.Index('idx_week_rank', 'week_start', 'week_end', 'rank_position'),
    )

    def to_json(self):
        """将模型对象转换为JSON格式的字典"""
        return {
            'id': self.id,
            'week_start': self.week_start.strftime('%Y-%m-%d') if self.week_start else None,
            'week_end': self.week_end.strftime('%Y-%m-%d') if self.week_end else None,
            'unit_name': self.unit_name,
            'article_count': self.article_count,
            'rank_position': self.rank_position,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }

    def __repr__(self):
        return f'<FxWeeklyStatistics {self.unit_name}: {self.article_count} articles>'


class FxWeeklySummary(db.Model):
    """
    普法工作管理系统本周汇总统计数据模型
    用于存储本周整体统计数据
    """
    __tablename__ = 'fx_weekly_summary'

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='自增主键')
    week_start = db.Column(db.Date, nullable=False, comment='统计周开始日期')
    week_end = db.Column(db.Date, nullable=False, comment='统计周结束日期')
    total_units = db.Column(db.Integer, nullable=False, default=0, comment='本周发布的单位总数')
    total_articles = db.Column(db.Integer, nullable=False, default=0, comment='本周发布的文章总数')
    avg_articles_per_unit = db.Column(db.Float, nullable=True, comment='平均每单位发布文章数')
    max_articles_unit = db.Column(db.String(100), nullable=True, comment='发布文章最多的单位')
    max_articles_count = db.Column(db.Integer, nullable=True, comment='最多发布文章数量')
    create_time = db.Column(db.DateTime, nullable=False, default=datetime.now, comment='记录创建时间')
    update_time = db.Column(db.DateTime, onupdate=datetime.now, comment='记录更新时间')

    # 创建唯一索引，确保每周只有一条汇总记录
    __table_args__ = (
        db.UniqueConstraint('week_start', 'week_end', name='uq_week_summary'),
    )

    def to_json(self):
        """将模型对象转换为JSON格式的字典"""
        return {
            'id': self.id,
            'week_start': self.week_start.strftime('%Y-%m-%d') if self.week_start else None,
            'week_end': self.week_end.strftime('%Y-%m-%d') if self.week_end else None,
            'total_units': self.total_units,
            'total_articles': self.total_articles,
            'avg_articles_per_unit': round(self.avg_articles_per_unit, 2) if self.avg_articles_per_unit else None,
            'max_articles_unit': self.max_articles_unit,
            'max_articles_count': self.max_articles_count,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }

    def __repr__(self):
        return f'<FxWeeklySummary {self.week_start} to {self.week_end}: {self.total_articles} articles>'
