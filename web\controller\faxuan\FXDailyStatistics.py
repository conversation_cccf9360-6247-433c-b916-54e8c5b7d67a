# 普法工作管理系统今日统计接口
# 用于统计今日各来源发布数

from datetime import datetime, date
from flask import jsonify
from flask_login import login_required
from sqlalchemy import func, and_, desc
from web.routes import main_routes
from web.models.faxuan.FXArticleRecords import FxArticleRecords
from web import db


@main_routes.route('/api/faxuan/daily/channel_stats', methods=['GET'])
# @login_required
def get_daily_channel_stats():
    """
    获取各来源今日发布数统计
    ---
    tags:
      - 普法今日统计
    description:
        获取各爬取渠道今日发布数统计，基于fx_article_records表的crawl_channel字段和publish_time字段
    responses:
      200:
          description: 各来源今日发布数统计
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 响应状态码
              msg:
                type: string
                description: 响应消息
              data:
                type: object
                properties:
                  channel_data:
                    type: array
                    description: 各渠道发布数据
                  today_date:
                    type: string
                    description: 统计日期
                  total_channels:
                    type: integer
                    description: 今日发布的渠道总数
                  total_articles:
                    type: integer
                    description: 今日发布的文章总数
      500:
          description: 服务器错误
    """
    try:
        # 获取今天的日期范围
        today = date.today()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())
        
        # 查询今日各渠道发布数统计，按发布数量降序排列
        channel_stats = db.session.query(
            FxArticleRecords.crawl_channel,
            func.count(FxArticleRecords.id).label('article_count')
        ).filter(
            and_(
                FxArticleRecords.publish_time >= today_start,
                FxArticleRecords.publish_time <= today_end
            )
        ).group_by(
            FxArticleRecords.crawl_channel
        ).order_by(
            desc(func.count(FxArticleRecords.id))
        ).all()
        
        # 构建渠道数据
        channel_data = []
        for i, (crawl_channel, article_count) in enumerate(channel_stats, 1):
            channel_data.append({
                'rank': i,
                'crawl_channel': crawl_channel,
                'article_count': article_count
            })
        
        # 获取今日发布的渠道总数
        total_channels = len(channel_stats)
        
        # 获取今日发布的文章总数
        total_articles = db.session.query(
            func.count(FxArticleRecords.id)
        ).filter(
            and_(
                FxArticleRecords.publish_time >= today_start,
                FxArticleRecords.publish_time <= today_end
            )
        ).scalar() or 0
        
        # 构建响应数据
        result = {
            'channel_data': channel_data,
            'today_date': today.strftime('%Y-%m-%d'),
            'total_channels': total_channels,
            'total_articles': total_articles
        }
        
        return jsonify({
            'code': 200,
            'msg': '操作成功',
            'data': result
        })
        
    except Exception as e:
        return jsonify({
            'code': 500,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500
