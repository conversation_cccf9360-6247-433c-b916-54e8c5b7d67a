# 普法工作管理系统本周统计接口
# 用于统计本周各单位发布数的TOP10和全部单位统计

from datetime import datetime, date, timedelta
from flask import request, jsonify
from flask_login import login_required
from sqlalchemy import func, and_, desc
from web.routes import main_routes
from web.models.faxuan.FXArticleRecords import FxArticleRecords
from web import db


def get_week_date_range():
    """
    获取本周的开始和结束日期（周一到周日）
    """
    today = date.today()
    # 获取本周一的日期（weekday()返回0-6，0是周一）
    days_since_monday = today.weekday()
    week_start = today - timedelta(days=days_since_monday)
    week_end = week_start + timedelta(days=6)
    
    # 转换为datetime对象，用于数据库查询
    week_start_datetime = datetime.combine(week_start, datetime.min.time())
    week_end_datetime = datetime.combine(week_end, datetime.max.time())
    
    return week_start_datetime, week_end_datetime, week_start.strftime('%Y-%m-%d'), week_end.strftime('%Y-%m-%d')


@main_routes.route('/api/faxuan/weekly/top10_units', methods=['GET'])
# @login_required
def get_weekly_top10_units():
    """
    获取本周各单位发布数统计TOP10
    ---
    tags:
      - 普法本周统计
    description:
        获取本周各单位发布数统计TOP10，基于fx_article_records表的publish_time字段
    responses:
      200:
          description: 本周各单位发布数统计TOP10
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 响应状态码
              msg:
                type: string
                description: 响应消息
              data:
                type: object
                properties:
                  top10_data:
                    type: array
                    description: TOP10单位数据
                  week_range:
                    type: string
                    description: 统计周期
                  total_units:
                    type: integer
                    description: 本周发布的单位总数
                  total_articles:
                    type: integer
                    description: 本周发布的文章总数
      500:
          description: 服务器错误
    """
    try:
        # 获取本周的日期范围
        week_start_datetime, week_end_datetime, week_start_str, week_end_str = get_week_date_range()
        
        # 查询本周各单位发布数统计，按发布数量降序排列，取前10名
        weekly_stats = db.session.query(
            FxArticleRecords.unit_name,
            func.count(FxArticleRecords.id).label('article_count')
        ).filter(
            and_(
                FxArticleRecords.publish_time >= week_start_datetime,
                FxArticleRecords.publish_time <= week_end_datetime
            )
        ).group_by(
            FxArticleRecords.unit_name
        ).order_by(
            desc(func.count(FxArticleRecords.id))
        ).limit(10).all()
        
        # 构建TOP10数据
        top10_data = []
        for i, (unit_name, article_count) in enumerate(weekly_stats, 1):
            top10_data.append({
                'rank': i,
                'unit_name': unit_name,
                'article_count': article_count
            })
        
        # 获取本周发布的单位总数
        total_units = db.session.query(
            func.count(func.distinct(FxArticleRecords.unit_name))
        ).filter(
            and_(
                FxArticleRecords.publish_time >= week_start_datetime,
                FxArticleRecords.publish_time <= week_end_datetime
            )
        ).scalar() or 0
        
        # 获取本周发布的文章总数
        total_articles = db.session.query(
            func.count(FxArticleRecords.id)
        ).filter(
            and_(
                FxArticleRecords.publish_time >= week_start_datetime,
                FxArticleRecords.publish_time <= week_end_datetime
            )
        ).scalar() or 0
        
        # 构建响应数据
        result = {
            'top10_data': top10_data,
            'week_range': f'{week_start_str} 至 {week_end_str}',
            'total_units': total_units,
            'total_articles': total_articles,
            'week_start': week_start_str,
            'week_end': week_end_str
        }
        
        return jsonify({
            'code': 200,
            'msg': '操作成功',
            'data': result
        })
        
    except Exception as e:
        return jsonify({
            'code': 500,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500


@main_routes.route('/api/faxuan/weekly/all_units', methods=['GET'])
# @login_required
def get_weekly_all_units():
    """
    获取本周各单位发布数统计（全部单位）
    ---
    tags:
      - 普法本周统计
    description:
        获取本周各单位发布数统计，包含所有有发布记录的单位，支持分页和搜索
    parameters:
      - name: page
        in: query
        type: integer
        description: 页码，默认为1
      - name: page_size
        in: query
        type: integer
        description: 每页数量，默认为20
      - name: unit_name
        in: query
        type: string
        description: 单位名称模糊查询
    responses:
      200:
          description: 本周各单位发布数统计
      500:
          description: 服务器错误
    """
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 20, type=int)
        unit_name_filter = request.args.get('unit_name', '', type=str)
        
        # 获取本周的日期范围
        week_start_datetime, week_end_datetime, week_start_str, week_end_str = get_week_date_range()
        
        # 构建查询条件
        filters = [
            FxArticleRecords.publish_time >= week_start_datetime,
            FxArticleRecords.publish_time <= week_end_datetime
        ]
        
        # 如果有单位名称过滤条件
        if unit_name_filter:
            filters.append(FxArticleRecords.unit_name.like(f'%{unit_name_filter}%'))
        
        # 查询本周各单位发布数统计，按发布数量降序排列
        query = db.session.query(
            FxArticleRecords.unit_name,
            func.count(FxArticleRecords.id).label('article_count')
        ).filter(
            and_(*filters)
        ).group_by(
            FxArticleRecords.unit_name
        ).order_by(
            desc(func.count(FxArticleRecords.id))
        )
        
        # 获取总记录数
        total_count = query.count()
        
        # 分页查询
        offset = (page - 1) * page_size
        weekly_stats = query.offset(offset).limit(page_size).all()
        
        # 构建单位数据
        units_data = []
        for i, (unit_name, article_count) in enumerate(weekly_stats, offset + 1):
            units_data.append({
                'rank': i,
                'unit_name': unit_name,
                'article_count': article_count
            })
        
        # 计算总页数
        total_pages = (total_count + page_size - 1) // page_size
        
        # 构建响应数据
        result = {
            'units_data': units_data,
            'week_range': f'{week_start_str} 至 {week_end_str}',
            'total_count': total_count,
            'page': page,
            'page_size': page_size,
            'total_pages': total_pages,
            'week_start': week_start_str,
            'week_end': week_end_str
        }
        
        return jsonify({
            'code': 200,
            'msg': '操作成功',
            'data': result
        })

    except Exception as e:
        return jsonify({
            'code': 500,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500
