from datetime import datetime

from web import db

class AnalysisSummary(db.Model):
    __tablename__ = 'fX_analysis_summary'

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='自增主键')
    unit_property = db.Column(db.String(50), nullable=False, comment='单位属性（市级政府、区级政府、高校、市属国企）')
    industry_system = db.Column(db.String(100), nullable=False, comment='行业系统（纪委监委系统、教育系统等）')
    total_articles = db.Column(db.Integer, nullable=False, comment='发布文章总数')
    total_views = db.Column(db.BigInteger, nullable=False, comment='总阅读量')
    max_article_views = db.Column(db.Integer, nullable=False, comment='单篇文章最高阅读量')
    month = db.Column(db.CHAR(7), nullable=False, comment='月份（格式：YYYY-MM）')
    total_likes = db.Column(db.BigInteger, nullable=False, comment='总点赞量')
    max_article_likes = db.Column(db.Integer, nullable=False, comment='单篇文章最高点赞量')
    total_comments = db.Column(db.BigInteger, nullable=False, comment='总评论量')
    max_article_comments = db.Column(db.Integer, nullable=False, comment='单篇文章最高评论量')
    create_time = db.Column(db.DateTime, nullable=False, default=datetime.now, comment='记录创建时间')
    update_time = db.Column(db.DateTime, onupdate=datetime.now, comment='记录更新时间')

    def to_json(self):
        """将模型对象转换为JSON格式的字典"""
        return {
            'id': self.id,
            'unit_property': self.unit_property,
            'industry_system': self.industry_system,
            'total_articles': self.total_articles,
            'total_views': self.total_views,
            'max_article_views': self.max_article_views,
            'month': self.month,
            'total_likes': self.total_likes,
            'max_article_likes': self.max_article_likes,
            'total_comments': self.total_comments,
            'max_article_comments': self.max_article_comments,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }
