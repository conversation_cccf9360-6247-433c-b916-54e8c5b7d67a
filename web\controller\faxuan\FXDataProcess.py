#该文件是普法宣传数据系统的中央调度枢纽，作为连接前端交互、AI分析和数据库存储的核心中间层，承担着三大关键角色：
# 数据通道：处理日均5000+次API请求
# 业务逻辑处理器：实现12类业务规则
from asyncio.log import logger
from datetime import datetime

from dateutil.relativedelta import relativedelta
from sqlalchemy import and_, func, distinct, join, extract, desc

from web.controller.faxuan.callDify import call_dify_workflow
from web.controller.faxuan.difyDataSave import dify_analysis_save
from web.routes import main_routes
from web.models.faxuan.FXArticleRecords import FxArticleRecords
from web.models.faxuan.FXEducationArticles import LegalEducationArticle
from web.models.faxuan.FXyuebao import Fxyuebao

from flask import Blueprint, request, jsonify
from web import db

from sqlalchemy import and_, func


# 分析助手筛选条件助手

@main_routes.route('/api/filter_articles_sum', methods=['POST'])
def filter_articles_sum():
    try:
        # 获取前端传来的筛选条件
        filters = request.get_json() or {}
        query_conditions = []

        # 单位属性 - 支持多选（OR逻辑）
        unit_property = filters.get('unit_property')
        if unit_property:
            # 如果包含逗号，说明是多选，需要拆分并使用OR条件
            if ',' in unit_property:
                unit_properties = [prop.strip() for prop in unit_property.split(',') if prop.strip()]
                if unit_properties:
                    from sqlalchemy import or_
                    unit_conditions = [LegalEducationArticle.unit_property.ilike(f'%{prop}%') for prop in unit_properties]
                    query_conditions.append(or_(*unit_conditions))
            else:
                query_conditions.append(LegalEducationArticle.unit_property.ilike(f'%{unit_property}%'))

        # 行业系统
        industry_system = filters.get('industry_system')
        if industry_system:
            query_conditions.append(LegalEducationArticle.industry_system.ilike(f'%{industry_system}%'))

        # 时间 (发布时间)
        publish_time_start = filters.get('publish_time_start')
        publish_time_end = filters.get('publish_time_end')
        if publish_time_start or publish_time_end:
            time_condition = []
            if publish_time_start:
                time_condition.append(FxArticleRecords.publish_time >= publish_time_start)
            if publish_time_end:
                time_condition.append(FxArticleRecords.publish_time <= publish_time_end)
            query_conditions.append(and_(*time_condition))

        # 来源 (爬取渠道)
        crawl_channel = filters.get('crawl_channel')
        if crawl_channel:
            query_conditions.append(FxArticleRecords.crawl_channel.ilike(f'%{crawl_channel}%'))

        # 所涉法律法规
        legal_content_type = filters.get('legal_content_type')
        if legal_content_type:
            query_conditions.append(LegalEducationArticle.legal_content_type.ilike(f'%{legal_content_type}%'))

        # 参与人数
        people_scale = filters.get('people_scale')
        if people_scale:
            query_conditions.append(LegalEducationArticle.people_scale.ilike(f'%{people_scale}%'))

        # 受众群体
        target_group = filters.get('target_group')
        if target_group:
            query_conditions.append(LegalEducationArticle.target_group.ilike(f'%{target_group}%'))

        # 分页参数
        page = int(filters.get('page', 1))  # 默认第1页
        per_page = int(filters.get('per_page', 10))  # 默认每页10条
        offset = (page - 1) * per_page

        # 聚合查询，包含 likes 和 comments
        query = db.session.query(
            LegalEducationArticle.unit_name,
            func.count(distinct(FxArticleRecords.article_id)).label('total_articles'),
            func.sum(FxArticleRecords.view_count).label('total_views'),
            func.max(FxArticleRecords.view_count).label('max_article_views'),
            func.sum(FxArticleRecords.likes).label('total_likes'),  # 新增
            func.max(FxArticleRecords.likes).label('max_article_likes'),  # 新增
            func.sum(FxArticleRecords.comments).label('total_comments'),  # 新增
            func.max(FxArticleRecords.comments).label('max_article_comments'),  # 新增
            LegalEducationArticle.month.label('publish_month')
        ).join(
            FxArticleRecords,
            FxArticleRecords.article_id == LegalEducationArticle.article_id
        )

        if query_conditions:
            query = query.filter(and_(*query_conditions))

        # 执行分页查询
        results = query.group_by(
            LegalEducationArticle.unit_name,
            LegalEducationArticle.month
        ).offset(offset).limit(per_page).all()

        # 格式化结果，统一空值处理
        response_data = []
        for result in results:
            item = {
                'unit': result[0] or '未知单位',
                'total_articles': result[1] if result[1] is not None else 0,
                'total_views': result[2] if result[2] is not None else 0,
                'max_article_views': result[3] if result[3] is not None else 0,
                'total_likes': result[4] if result[4] is not None else 0,  # 使用实际数据
                'max_article_likes': result[5] if result[5] is not None else 0,  # 使用实际数据
                'total_comments': result[6] if result[6] is not None else 0,  # 使用实际数据
                'max_article_comments': result[7] if result[7] is not None else 0,  # 使用实际数据
                'publish_month': result[8] or '未知月份'
            }
            response_data.append(item)

        # 获取总记录数
        total_query = query.group_by(LegalEducationArticle.unit_name, LegalEducationArticle.month)
        total = total_query.count() if query_conditions else total_query.count()

        return jsonify({
            'status': 'success',
            'data': response_data,
            'total': total,
            'page': page,
            'per_page': per_page
        })

    except Exception as e:
        logger.error(f"Error in filter_articles_sum: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)}), 500






@main_routes.route('/api/filter_articles_detail', methods=['POST'])
def filter_articles_detail():
    try:
        # 获取前端传来的筛选条件
        filters = request.get_json() or {}
        query_conditions = []

        # 单位名称
        unit_name = filters.get('unit_name')
        if unit_name:
            query_conditions.append(LegalEducationArticle.unit_name.ilike(f'%{unit_name}%'))

        # 单位属性 - 支持多选（OR逻辑）
        unit_property = filters.get('unit_property')
        if unit_property:
            # 如果包含逗号，说明是多选，需要拆分并使用OR条件
            if ',' in unit_property:
                unit_properties = [prop.strip() for prop in unit_property.split(',') if prop.strip()]
                if unit_properties:
                    from sqlalchemy import or_
                    unit_conditions = [LegalEducationArticle.unit_property.ilike(f'%{prop}%') for prop in unit_properties]
                    query_conditions.append(or_(*unit_conditions))
            else:
                query_conditions.append(LegalEducationArticle.unit_property.ilike(f'%{unit_property}%'))

        # 行业系统
        industry_system = filters.get('industry_system')
        if industry_system:
            query_conditions.append(LegalEducationArticle.industry_system.ilike(f'%{industry_system}%'))

        # 时间 (发布时间)
        publish_time_start = filters.get('publish_time_start')
        publish_time_end = filters.get('publish_time_end')
        if publish_time_start or publish_time_end:
            time_condition = []
            if publish_time_start:
                time_condition.append(FxArticleRecords.publish_time >= publish_time_start)
            if publish_time_end:
                time_condition.append(FxArticleRecords.publish_time <= publish_time_end)
            query_conditions.append(and_(*time_condition))

        # 来源 (爬取渠道)
        crawl_channel = filters.get('crawl_channel')
        if crawl_channel:
            query_conditions.append(FxArticleRecords.crawl_channel.ilike(f'%{crawl_channel}%'))

        # 所涉法律法规
        legal_content_type = filters.get('legal_content_type')
        if legal_content_type:
            query_conditions.append(LegalEducationArticle.legal_content_type.ilike(f'%{legal_content_type}%'))

        # 参与人数
        people_scale = filters.get('people_scale')
        if people_scale:
            query_conditions.append(LegalEducationArticle.people_scale.ilike(f'%{people_scale}%'))

        # 受众群体
        target_group = filters.get('target_group')
        if target_group:
            query_conditions.append(LegalEducationArticle.target_group.ilike(f'%{target_group}%'))

        # 分页参数
        page = int(filters.get('page', 1))  # 默认第1页
        per_page = int(filters.get('per_page', 10))  # 默认每页10条
        offset = (page - 1) * per_page

        # 使用 INNER JOIN 确保两表都匹配
        query = db.session.query(
            FxArticleRecords.article_id,
            FxArticleRecords.unit_name,
            LegalEducationArticle.unit_property,
            LegalEducationArticle.industry_system,
            FxArticleRecords.publish_time,
            FxArticleRecords.crawl_channel,
            LegalEducationArticle.legal_content_type,
            LegalEducationArticle.people_scale,
            LegalEducationArticle.target_group,
            FxArticleRecords.view_count,
            FxArticleRecords.likes,
            FxArticleRecords.comments,
            FxArticleRecords.article_title,
            FxArticleRecords.article_url
        ).join(
            LegalEducationArticle,
            FxArticleRecords.article_id == LegalEducationArticle.article_id
        )

        if query_conditions:
            query = query.filter(and_(*query_conditions))

        # 执行分页查询
        results = query.offset(offset).limit(per_page).all()

        # 格式化结果，统一空值处理
        response_data = []
        for result in results:
            item = {
                'article_id': result[0],
                'unit_name': result[1] or '未知单位',
                'unit_property': result[2] or '未知属性',
                'industry_system': result[3] or '未知系统',
                'publish_time': result[4].isoformat() if result[4] else None,
                'crawl_channel': result[5] or '未知渠道',
                'legal_content_type': result[6] or '未知类型',
                'people_scale': result[7] or '未知规模',
                'target_group': result[8] or '未知群体',
                'view_count': result[9] if result[9] is not None else 0,
                'likes': result[10] if result[10] is not None else 0,
                'comments': result[11] if result[11] is not None else 0,
                'article_title': result[12] or '未知标题',
                'article_url': result[13] if result[13] else None
            }
            response_data.append(item)

        # 获取总记录数
        total_query = query
        total = total_query.count() if query_conditions else total_query.count()

        return jsonify({
            'status': 'success',
            'data': response_data,
            'total': total,
            'page': page,
            'per_page': per_page
        })

    except Exception as e:
        logger.error(f"Error in filter_articles_detail: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)}), 500


# 数据传递给dify工作流
@main_routes.route('/api/connect_dify_analysis', methods=['POST'])
def connect_dify_analysis():
    """
    接收文章信息并提取参数。
    """
    try:
        # 从请求中提取 JSON 数据
        data = request.get_json()
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400

        # 提取四个参数
        wenzhang_id = data.get("wenzhang_id")
        danweimingcheng = data.get("danweimingcheng")
        wenzhang_biaoti = data.get("wenzhang_biaoti")
        wenzhang_neirong = data.get("wenzhang_neirong")
        dify_result=call_dify_workflow(wenzhang_id, danweimingcheng, wenzhang_biaoti, wenzhang_neirong)

        dify_analysis_save(dify_result)

        # 验证参数是否完整
        if not all([wenzhang_id, danweimingcheng, wenzhang_biaoti, wenzhang_neirong]):
            missing = [key for key, value in {
                "wenzhang_id": wenzhang_id,
                "danweimingcheng": danweimingcheng,
                "wenzhang_biaoti": wenzhang_biaoti,
                "wenzhang_neirong": wenzhang_neirong
            }.items() if value is None]
            return jsonify({"error": f"Missing required fields: {', '.join(missing)}"}), 400

        # 返回提取的参数
        return jsonify({
            "status": "success",
            "data": {
                "wenzhang_id": wenzhang_id,
                "danweimingcheng": danweimingcheng,
                "wenzhang_biaoti": wenzhang_biaoti,
                "wenzhang_neirong": wenzhang_neirong
            }
        })

    except Exception as e:
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500





# 根据月份获取计算数据
def get_month_stats(year, month):
    """
    计算指定年月的统计数据
    """
    articles_count = db.session.query(func.count(FxArticleRecords.id)).filter(
        extract('year', FxArticleRecords.publish_time) == year,
        extract('month', FxArticleRecords.publish_time) == month
    ).scalar() or 0

    total_views = db.session.query(func.sum(FxArticleRecords.view_count)).filter(
        extract('year', FxArticleRecords.publish_time) == year,
        extract('month', FxArticleRecords.publish_time) == month
    ).scalar() or 0

    total_likes = db.session.query(func.sum(FxArticleRecords.likes)).filter(
        extract('year', FxArticleRecords.publish_time) == year,
        extract('month', FxArticleRecords.publish_time) == month
    ).scalar() or 0

    return {
        "year_month": f"{year}年{month}月",
        "articles_count": articles_count,
        "total_views": total_views,
        "total_likes": total_likes
    }


def get_unit_stats(year, month):
    """
    计算指定年月的统计数据
    """
    articles_count = db.session.query(func.count(LegalEducationArticle.id)).filter(
        extract('year', FxArticleRecords.publish_time) == year,
        extract('month', FxArticleRecords.publish_time) == month
    ).scalar() or 0

    total_views = db.session.query(func.sum(FxArticleRecords.view_count)).filter(
        extract('year', FxArticleRecords.publish_time) == year,
        extract('month', FxArticleRecords.publish_time) == month
    ).scalar() or 0

    total_likes = db.session.query(func.sum(FxArticleRecords.likes)).filter(
        extract('year', FxArticleRecords.publish_time) == year,
        extract('month', FxArticleRecords.publish_time) == month
    ).scalar() or 0

    return {
    }

# 计算单位的阅读报告
def get_unit_stats(year, month):
    """
    计算指定年月的统计数据，按unit_property分组
    """
    month_str = f"{year:04d}-{month:02d}"
    # Define the unit properties to query
    # unit_properties = ['市级单位', '区级单位', '高校', '国企']
    # Query distinct unit_property values for the specified month
    unit_properties = db.session.query(distinct(LegalEducationArticle.unit_property)).filter(
        LegalEducationArticle.month == month_str
    ).all()

    # Extract unit_property values from query result (flatten the list of tuples)
    unit_properties = [prop[0] for prop in unit_properties if prop[0]]

    # Initialize result dictionary
    stats = {}

    # Format the month string to match the 'YYYY-MM' format in the database
    month_str = f"{year:04d}-{month:02d}"

    for prop in unit_properties:
        # Query for articles count
        articles_count = db.session.query(func.count(LegalEducationArticle.id)).filter(
            LegalEducationArticle.month == month_str,
            LegalEducationArticle.unit_property == prop
        ).scalar() or 0

        # Query for total views
        total_views = db.session.query(func.sum(FxArticleRecords.view_count)).join(
            LegalEducationArticle,
            LegalEducationArticle.article_id == FxArticleRecords.article_id
        ).filter(
            LegalEducationArticle.month == month_str,
            LegalEducationArticle.unit_property == prop
        ).scalar() or 0

        # Query for total likes
        total_likes = db.session.query(func.sum(FxArticleRecords.likes)).join(
            LegalEducationArticle,
            LegalEducationArticle.article_id == FxArticleRecords.article_id
        ).filter(
            LegalEducationArticle.month == month_str,
            LegalEducationArticle.unit_property == prop
        ).scalar() or 0

        # Store stats for this unit property
        stats[prop] = {
            'articles_count': articles_count,
            'total_views': total_views,
            'total_likes': total_likes
        }

    return stats


def get_system_stats(year, month):
    """
    计算指定年月的统计数据，按industry_system分组，动态获取industry_system列表
    """
    # Format the month string to match the 'YYYY-MM' format in the database
    month_str = f"{year:04d}-{month:02d}"

    # Query distinct industry_system values for the specified month
    industry_systems = db.session.query(distinct(LegalEducationArticle.industry_system)).filter(
        LegalEducationArticle.month == month_str
    ).all()

    # Extract industry_system values from query result (flatten the list of tuples)
    industry_systems = [system[0] for system in industry_systems if system[0]]

    # Initialize result dictionary
    stats = {}

    for system in industry_systems:
        # Query for articles count
        articles_count = db.session.query(func.count(LegalEducationArticle.id)).filter(
            LegalEducationArticle.month == month_str,
            LegalEducationArticle.industry_system == system
        ).scalar() or 0

        # Query for total views
        total_views = db.session.query(func.sum(FxArticleRecords.view_count)).join(
            LegalEducationArticle,
            LegalEducationArticle.article_id == FxArticleRecords.article_id
        ).filter(
            LegalEducationArticle.month == month_str,
            LegalEducationArticle.industry_system == system
        ).scalar() or 0

        # Query for total likes
        total_likes = db.session.query(func.sum(FxArticleRecords.likes)).join(
            LegalEducationArticle,
            LegalEducationArticle.article_id == FxArticleRecords.article_id
        ).filter(
            LegalEducationArticle.month == month_str,
            LegalEducationArticle.industry_system == system
        ).scalar() or 0

        # Store stats for this industry system
        stats[system] = {
            'articles_count': articles_count,
            'total_views': total_views,
            'total_likes': total_likes
        }

    return stats


def get_top10_units(year, month):
    """
    获取指定年月发布文章总数前top10的单位数据
    """
    # Format the month string to match the 'YYYY-MM' format in the database
    month_str = f"{year:04d}-{month:02d}"

    # Query for top 10 units by articles count
    top_units_query = db.session.query(
        LegalEducationArticle.unit_name,
        LegalEducationArticle.industry_system,
        func.count(LegalEducationArticle.id).label('articles_count'),
        func.sum(FxArticleRecords.view_count).label('total_views'),
        func.max(FxArticleRecords.view_count).label('max_view'),
        func.sum(FxArticleRecords.likes).label('total_likes'),
        func.max(FxArticleRecords.likes).label('max_like')
    ).join(
        FxArticleRecords,
        LegalEducationArticle.article_id == FxArticleRecords.article_id
    ).filter(
        LegalEducationArticle.month == month_str
    ).group_by(
        LegalEducationArticle.unit_name,
        LegalEducationArticle.industry_system
    ).order_by(
        func.count(LegalEducationArticle.id).desc()
    ).limit(10).all()

    # Convert query results to list of dictionaries
    top_units = [
        {
            'unit_name': row.unit_name,
            'industry_system': row.industry_system,
            'articles_count': row.articles_count,
            'total_views': row.total_views or 0,
            'max_view': row.max_view or 0,
            'total_likes': row.total_likes or 0,
            'max_like': row.max_like or 0
        } for row in top_units_query
    ]

    return top_units


def get_max_article_stats(year, month):
    """
    获取指定年月单篇文章最高阅读量和最高点赞量的文章数据
    """
    # Format the month string to match the 'YYYY-MM' format in the database
    month_str = f"{year:04d}-{month:02d}"

    # Query for the article with the highest view_count
    max_view_article = db.session.query(
        LegalEducationArticle.unit_name,
        FxArticleRecords.article_title,
        FxArticleRecords.article_content,
        FxArticleRecords.view_count,
        FxArticleRecords.likes
    ).join(
        FxArticleRecords,
        LegalEducationArticle.article_id == FxArticleRecords.article_id
    ).filter(
        LegalEducationArticle.month == month_str
    ).order_by(
        desc(FxArticleRecords.view_count)
    ).limit(1).first()

    # Query for the article with the highest likes
    max_like_article = db.session.query(
        LegalEducationArticle.unit_name,
        FxArticleRecords.article_title,
        FxArticleRecords.article_content,
        FxArticleRecords.view_count,
        FxArticleRecords.likes
    ).join(
        FxArticleRecords,
        LegalEducationArticle.article_id == FxArticleRecords.article_id
    ).filter(
        LegalEducationArticle.month == month_str
    ).order_by(
        desc(FxArticleRecords.likes)
    ).limit(1).first()

    # Prepare the results
    max_view = {
        'unit_name': max_view_article.unit_name if max_view_article else None,
        'article_title': max_view_article.article_title if max_view_article else None,
        'article_content': max_view_article.article_content if max_view_article else None,
        'view_count': max_view_article.view_count if max_view_article else 0,
        'likes': max_view_article.likes if max_view_article else 0
    } if max_view_article else None

    max_like = {
        'unit_name': max_like_article.unit_name if max_like_article else None,
        'article_title': max_like_article.article_title if max_like_article else None,
        'article_content': max_like_article.article_content if max_like_article else None,
        'view_count': max_like_article.view_count if max_like_article else 0,
        'likes': max_like_article.likes if max_like_article else 0
    } if max_like_article else None

    return {
        'max_view': max_view,
        'max_like': max_like
    }


# 按照普法对象统计
def target_group_stats(year, month):
    """
    统计指定年月不同目标群体的文章数据
    :param year: 年份
    :param month: 月份
    :return: 包含各目标群体统计数据的字典
    """
    try:
        # 定义目标群体列表
        target_groups = ['领导干部', '基层干部', '企业人员', '青少年', '新业态就业群体']

        # 初始化结果字典
        results = {}

        # 格式化月份字符串为YYYY-MM
        month_str = f"{year}-{month:02d}"

        for group in target_groups:
            # 构建查询条件
            conditions = [
                LegalEducationArticle.type_class == 1,
                LegalEducationArticle.target_group.like(f'%{group}%'),
                LegalEducationArticle.month == month_str
            ]

            # 查询符合条件的文章ID
            article_ids = db.session.query(LegalEducationArticle.article_id).filter(
                *conditions
            ).all()

            # 提取article_id列表
            article_ids = [id[0] for id in article_ids]

            if not article_ids:
                # 如果没有找到相关文章，记录0值
                results[group] = {
                    'sum_count': 0,
                    'sum_view_count': 0,
                    'sum_likes': 0
                }
                continue

            # 查询fx_article_records表中对应文章的统计数据
            stats = db.session.query(
                func.count(FxArticleRecords.id).label('sum_count'),
                func.sum(FxArticleRecords.view_count).label('sum_view_count'),
                func.sum(FxArticleRecords.likes).label('sum_likes')
            ).filter(
                FxArticleRecords.article_id.in_(article_ids)
            ).first()

            # 记录结果
            results[group] = {
                'sum_count': stats.sum_count or 0,
                'sum_view_count': stats.sum_view_count or 0,
                'sum_likes': stats.sum_likes or 0
            }

        # 构建返回数据
        return {
            'lingdaoganbu_fbwzzs': results.get('领导干部', {}).get('sum_count', 0),
            'lingdaoganbu_zydl': results.get('领导干部', {}).get('sum_view_count', 0),
            'lingdaoganbu_zdzl': results.get('领导干部', {}).get('sum_likes', 0),
            'jicengganbu_fbwzzs': results.get('基层干部', {}).get('sum_count', 0),
            'jicengganbu_zydl': results.get('基层干部', {}).get('sum_view_count', 0),
            'jicengganbu_zdzl': results.get('基层干部', {}).get('sum_likes', 0),
            'qiyerenyuan_fbwzzs': results.get('企业人员', {}).get('sum_count', 0),
            'qiyerenyuan_zydl': results.get('企业人员', {}).get('sum_view_count', 0),
            'qiyerenyuan_zdzl': results.get('企业人员', {}).get('sum_likes', 0),
            'qingshaonian_fbwzzs': results.get('青少年', {}).get('sum_count', 0),
            'qingshaonian_zydl': results.get('青少年', {}).get('sum_view_count', 0),
            'qingshaonian_zdzl': results.get('青少年', {}).get('sum_likes', 0),
            'xinyetai_fbwzzs': results.get('新业态就业群体', {}).get('sum_count', 0),
            'xinyetai_zydl': results.get('新业态就业群体', {}).get('sum_view_count', 0),
            'xinyetai_zdzl': results.get('新业态就业群体', {}).get('sum_likes', 0)
        }

    except Exception as e:
        logger.error(f"Error in target_group_stats: {str(e)}", exc_info=True)
        return {
            'status': 'error',
            'message': str(e)
        }




# 指定月份计算全部数据信息
@main_routes.route('/api/filter_month_articles', methods=['POST'])
def filter_month_articles():
    try:
        # 从请求体中获取JSON数据
        data = request.get_json()
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400

        # 从JSON数据中获取年份-月份（格式：YYYY-MM）
        year_month = data.get('month')

        if not year_month:
            return jsonify({"error": "Please provide a valid year-month (e.g., 2025-7)"}), 400

        try:
            # 解析年份和月份
            year, month = map(int, year_month.split('-'))
            if month < 1 or month > 12 or year < 1900 or year > 9999:
                raise ValueError("Invalid year or month")
        except ValueError:
            return jsonify({"error": "Invalid year-month format. Use YYYY-MM (e.g., 2025-7)"}), 400

        # 计算当前月的统计
        current_stats = get_month_stats(year, month)

        # 计算上个月的年份和月份
        current_date = datetime(year, month, 1)
        last_month_date = current_date - relativedelta(months=1)
        last_year = last_month_date.year
        last_month = last_month_date.month

        # 计算上个月的统计
        last_stats = get_month_stats(last_year, last_month)
        unit_stats = get_unit_stats(year, month)
        system_stats = get_system_stats(year, month)
        top10_units = get_top10_units(year, month)
        max_article_stats = get_max_article_stats(year, month)

        # 计算目标群体统计
        target_stats = target_group_stats(year, month)

        # 构建响应数据
        result = {
            "current": current_stats,
            "last": last_stats,
            "unit_stats": unit_stats,
            "system_stats": system_stats,
            "top10_units": top10_units,
            "max_article_stats": max_article_stats,
            "target_group_stats": target_stats
        }

        return jsonify(result), 200

    except Exception as e:
        logger.error(f"Error in filter_month_articles: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)}), 500


# 前端_月报列表接口
@main_routes.route('/api/get_yuebao_list', methods=['POST'])
def get_yuebao_list():
    """
    获取指定月份的月报列表，返回time_period和title两个字段
    POST请求，接收month参数(格式：YYYY-MM)
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400

        # 获取month参数
        year_month = data.get('month')
        if not year_month:
            return jsonify({"error": "Please provide month parameter (e.g., 2025-7)"}), 400

        try:
            # 解析年份和月份
            year, month = map(int, year_month.split('-'))
            if month < 1 or month > 12 or year < 1900 or year > 9999:
                raise ValueError("Invalid year or month")
        except ValueError:
            return jsonify({"error": "Invalid month format. Use YYYY-MM (e.g., 2025-7)"}), 400

        # 格式化月份字符串为YYYY-MM
        month_str = f"{year}-{month:02d}"

        # 查询指定月份的月报列表
        results = db.session.query(
            Fxyuebao.time_period,
            Fxyuebao.title
        ).filter(
            Fxyuebao.time_period.like(f"{month_str}%")  # 使用like匹配指定月份
        ).order_by(
            desc(Fxyuebao.time_period)  # 按时间降序排列
        ).all()

        # 格式化结果
        yuebao_list = [
            {
                'time_period': result.time_period,
                'title': result.title
            } for result in results
        ]

        return jsonify({
            'status': 'success',
            'data': yuebao_list,
            'month': month_str  # 返回查询的月份
        }), 200

    except Exception as e:
        logger.error(f"Error in get_yuebao_list: {str(e)}", exc_info=True)
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500



# 前端_月报详情接口
@main_routes.route('/api/get_yuebao_content', methods=['POST'])
def get_yuebao_content():
    """
    获取指定月份的月报内容，返回content字段
    POST请求，接收month参数(格式：YYYY-MM)
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400

        # 获取month参数
        year_month = data.get('month')
        if not year_month:
            return jsonify({"error": "Please provide month parameter (e.g., 2025-7)"}), 400

        try:
            # 解析年份和月份
            year, month = map(int, year_month.split('-'))
            if month < 1 or month > 12 or year < 1900 or year > 9999:
                raise ValueError("Invalid year or month")
        except ValueError:
            return jsonify({"error": "Invalid month format. Use YYYY-MM (e.g., 2025-7)"}), 400

        # 格式化月份字符串为YYYY-MM
        month_str = f"{year}-{month:02d}"

        # 查询指定月份的月报内容
        result = db.session.query(
            Fxyuebao.content
        ).filter(
            Fxyuebao.time_period.like(f"{month_str}%")  # 使用like匹配指定月份
        ).order_by(
            desc(Fxyuebao.time_period)  # 按时间降序排列，获取最新的月报
        ).first()

        if not result:
            return jsonify({
                'status': 'error',
                'message': f'No content found for month {month_str}'
            }), 404

        return jsonify({
            'status': 'success',
            'data': {
                'content': result[0],  # 直接返回content字段内容
                'month': month_str     # 返回查询的月份
            }
        }), 200

    except Exception as e:
        logger.error(f"Error in get_yuebao_content: {str(e)}", exc_info=True)
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500



