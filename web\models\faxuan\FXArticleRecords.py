from datetime import datetime
from web import db

class FxArticleRecords(db.Model):
    __tablename__ = 'fx_article_records'

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='自增主键')
    crawl_time = db.Column(db.DateTime, nullable=False, comment='爬取时间')
    crawl_channel = db.Column(db.String(50), nullable=False, comment='爬取渠道（官网、微信公众号等）')
    unit_name = db.Column(db.String(100), nullable=False, comment='单位名称')
    article_title = db.Column(db.String(255), nullable=False, comment='文章标题')
    article_content = db.Column(db.Text, comment='文章内容')
    publish_time = db.Column(db.DateTime, nullable=True, comment='文章发布时间')
    view_count = db.Column(db.Integer, nullable=True, comment='浏览次数')
    article_url = db.Column(db.String(500), nullable=True, comment='文章链接')
    article_id = db.Column(db.String(100), nullable=False, comment='文章ID（来源系统中的唯一标识）')
    # likes = db.Column(db.Integer, nullable=True, comment='点赞量')  # 新增字段，数据库中暂未添加
    # comments = db.Column(db.Integer, nullable=True, comment='评论量')  # 新增字段，数据库中暂未添加
    create_time = db.Column(db.DateTime, nullable=False, default=datetime.now, comment='记录创建时间')
    update_time = db.Column(db.DateTime, onupdate=datetime.now, comment='记录更新时间')
    # create_by = db.Column(db.String(100), comment='创建人')  # 可选，视需求保留或移除

    def to_json(self):
        """将模型对象转换为JSON格式的字典"""
        return {
            'id': self.id,
            'crawl_time': self.crawl_time.isoformat() if self.crawl_time else None,
            'crawl_channel': self.crawl_channel,
            'unit_name': self.unit_name,
            'article_title': self.article_title,
            'article_content': self.article_content,
            'publish_time': self.publish_time.isoformat() if self.publish_time else None,
            'view_count': self.view_count,
            'article_url': self.article_url,
            'article_id': self.article_id,
            # 'likes': self.likes,  # 数据库中暂未添加此字段
            # 'comments': self.comments,  # 数据库中暂未添加此字段
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }